import { GenerationType, MessageRole } from 'generated/prisma';

export interface AssistantMessage {
  role: MessageRole;
  content: string;
  inputData?: any;
  outputData?: any;
}

/**
 * Assistant request interface optimized for chat completion API
 * Supports conversational context and message history
 */
export interface AssistantRequest {
  generationType: GenerationType;
  conversationHistory: AssistantMessage[];
  userMessage: string;
  inputData?: any;
  metadata?: {
    projectContext?: string;
    previousGenerations?: string[];
    userPreferences?: any;
  };
}

export interface AssistantResponse {
  content: string;
  outputData?: {
    code?: string;
    documentation?: string;
    metadata?: any;
  };
  processingTime: number;
  model?: string;
  tokensUsed?: number;
  provider?: HuggingFaceProvider;
}

export type HuggingFaceProvider = string;

export interface HuggingFaceConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel?: string;
  uiModel?: string;
  documentationModel?: string;
  maxTokens?: number;
  temperature?: number;
  defaultProvider?: HuggingFaceProvider;
  uiProvider?: HuggingFaceProvider;
  documentationProvider?: HuggingFaceProvider;
}

export interface ModelResponse {
  generated_text?: string;
  error?: string;
  estimated_time?: number;
}

export interface TextGenerationParameters {
  max_new_tokens?: number;
  temperature?: number;
  top_p?: number;
  top_k?: number;
  repetition_penalty?: number;
  return_full_text?: boolean;
  do_sample?: boolean;
  stop?: string[];
}
