import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  InferenceClient,
  InferenceClientError,
  InferenceClientProviderApiError,
  InferenceClientProviderOutputError,
  InferenceClientInputError,
  InferenceProvider,
} from '@huggingface/inference';
import { GenerationType } from 'generated/prisma';
import {
  AssistantRequest,
  AssistantResponse,
  HuggingFaceConfig,
} from './interfaces/assistant.interface';

/**
 * AssistantService - AI-powered code and documentation generation
 *
 * Features:
 * - Hugging Face Inference Client integration
 * - Support for UI and documentation generation
 * - Conversational context support
 * - Proper error handling and fallbacks
 */
@Injectable()
export class AssistantService {
  private readonly logger = new Logger(AssistantService.name);
  private readonly hf: InferenceClient;
  private readonly config: HuggingFaceConfig;

  /**
   * Helper method to safely extract error message from unknown error types
   */
  private getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'Unknown error';
  }

  constructor(private configService: ConfigService) {
    this.config = {
      apiKey: this.configService.get<string>('HUGGINGFACE_API_KEY') || '',
      baseUrl: this.configService.get<string>('HUGGINGFACE_BASE_URL'),
      defaultModel:
        this.configService.get<string>('HUGGINGFACE_DEFAULT_MODEL') ||
        'meta-llama/Llama-3.2-3B-Instruct',
      uiModel:
        this.configService.get<string>('HUGGINGFACE_UI_MODEL') ||
        'meta-llama/CodeLlama-7b-Instruct-hf',
      documentationModel:
        this.configService.get<string>('HUGGINGFACE_DOCS_MODEL') ||
        'meta-llama/Llama-3.2-3B-Instruct',
      maxTokens: parseInt(
        this.configService.get<string>('HUGGINGFACE_MAX_TOKENS') || '1024',
      ),
      temperature: parseFloat(
        this.configService.get<string>('HUGGINGFACE_TEMPERATURE') || '0.7',
      ),
      defaultProvider:
        this.configService.get<string>('HUGGINGFACE_DEFAULT_PROVIDER') ||
        'hf-inference',
      uiProvider:
        this.configService.get<string>('HUGGINGFACE_UI_PROVIDER') ||
        'hf-inference',
      documentationProvider:
        this.configService.get<string>('HUGGINGFACE_DOCS_PROVIDER') ||
        'hf-inference',
    };

    if (!this.config.apiKey) {
      this.logger.warn(
        'HUGGINGFACE_API_KEY not found. Assistant service will use public inference API with rate limits.',
      );
    }

    // Initialize InferenceClient
    this.hf = new InferenceClient(this.config.apiKey || undefined);
  }

  /**
   * Process assistant request using chat completion API for conversational AI
   */
  async processRequest(request: AssistantRequest): Promise<AssistantResponse> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `Processing ${request.generationType} generation request using chat completion`,
      );

      const model = this.getModelForType(request.generationType);

      const conversationContext = this.buildConversationContext(request);

      let response: AssistantResponse;

      if (request.generationType === GenerationType.UI) {
        response = await this.generateUICode(
          conversationContext,
          model,
          startTime,
        );
      } else {
        response = await this.generateDocumentation(
          conversationContext,
          model,
          startTime,
        );
      }

      this.logger.log(
        `Generated ${request.generationType} response in ${response.processingTime}ms`,
      );
      return response;
    } catch (error: unknown) {
      let errorMessage = 'Unknown error';

      if (error instanceof InferenceClientError) {
        errorMessage = `Inference Client Error: ${error.message}`;
      } else {
        errorMessage = this.getErrorMessage(error);
      }

      this.logger.error(`Assistant processing failed: ${errorMessage}`, error);
      throw new InternalServerErrorException(
        'Failed to process assistant request',
      );
    }
  }

  private getModelForType(type: GenerationType): string {
    switch (type) {
      case GenerationType.UI:
        return this.config.uiModel!;
      case GenerationType.DOCUMENTATION:
        return this.config.documentationModel!;
      default:
        return this.config.defaultModel!;
    }
  }

  private buildConversationContext(request: AssistantRequest): string {
    let context = '';

    // Add system context based on generation type
    if (request.generationType === GenerationType.UI) {
      context +=
        'You are an expert React/TypeScript developer specializing in creating modern, responsive UI components with Tailwind CSS. ';
      context +=
        'Generate clean, production-ready code with proper TypeScript types and accessibility features.\n\n';
      context +=
        'The components are meant to run with vite, so do not use Next.js, only use lucide-react for icons.\n\n';
      context +=
        'The generated component should be called App and exported by default.\n\n';
    } else {
      context +=
        'You are a technical documentation expert. Create clear, comprehensive documentation with examples and best practices.\n\n';
    }

    if (request.metadata?.projectContext) {
      context += `Project Context: ${request.metadata.projectContext}\n\n`;
    }

    if (request.conversationHistory.length > 0) {
      context += 'Previous conversation:\n';
      request.conversationHistory.forEach((message) => {
        if (message.role === 'USER') {
          context += `User: ${message.content}\n`;
        } else if (message.role === 'ASSISTANT') {
          context += `Assistant: ${message.content}\n`;
        }
      });
      context += '\n';
    }

    context += `Current request: ${request.userMessage}`;

    return context;
  }

  private async generateUICode(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      // Get compatible model and provider for UI generation
      const { model: compatibleModel, provider } =
        this.getCompatibleModelAndProvider(GenerationType.UI);

      const messages = [
        {
          role: 'system',
          content:
            'You are an expert React TypeScript developer. Generate clean, modern, accessible components with Tailwind CSS.',
        },
        {
          role: 'user',
          content: `${context}\n\nGenerate a React TypeScript component with Tailwind CSS. Include:\n1. Proper TypeScript interfaces\n2. Responsive design\n3. Accessibility features\n4. Clean, modern styling\n\nComponent code:`,
        },
      ];

      const result = await this.hf.chatCompletion({
        model: compatibleModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        provider: provider as InferenceProvider,
      });

      const generatedCode = result.choices?.[0]?.message?.content || '';

      return {
        content: generatedCode,
        outputData: {
          code: generatedCode,
          metadata: {
            framework: 'React',
            styling: 'Tailwind CSS',
            language: 'TypeScript',
            model: compatibleModel,
            tokensGenerated: generatedCode.split(' ').length,
          },
        },
        processingTime: Date.now() - startTime,
        model: compatibleModel,
      };
    } catch (error: any) {
      let errorMessage = 'Unknown error';
      let errorType = 'UnknownError';

      if (error instanceof InferenceClientProviderApiError) {
        errorMessage = `Provider API Error: ${error.message}`;
        errorType = 'InferenceClientProviderApiError';
      } else if (error instanceof InferenceClientProviderOutputError) {
        errorMessage = `Provider Output Error: ${error.message}`;
        errorType = 'InferenceClientProviderOutputError';
      } else if (error instanceof InferenceClientInputError) {
        errorMessage = `Input Error: ${error.message}`;
        errorType = 'InferenceClientInputError';
      } else if (error instanceof InferenceClientError) {
        errorMessage = `Inference Error: ${error.message}`;
        errorType = 'InferenceClientError';
      } else if (error instanceof Error) {
        errorMessage = error.message;
        errorType = error.constructor.name;
      }

      this.logger.error(`UI generation failed: ${errorMessage}`);

      return {
        content:
          'I apologize, but I encountered an error while generating your UI component. Please try again with a different prompt.',
        outputData: {
          code: '// Error occurred during generation',
          metadata: {
            error: true,
            errorMessage,
            errorType,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    }
  }

  private async generateDocumentation(
    context: string,
    model: string,
    startTime: number,
  ): Promise<AssistantResponse> {
    try {
      // Get compatible model and provider for documentation generation
      const { model: compatibleModel, provider } =
        this.getCompatibleModelAndProvider(GenerationType.DOCUMENTATION);

      const messages = [
        {
          role: 'system',
          content:
            'You are a technical documentation expert. Generate comprehensive, clear, and well-structured documentation with practical examples.',
        },
        {
          role: 'user',
          content: `${context}\n\nGenerate comprehensive technical documentation. Include:\n1. Clear explanations\n2. Code examples\n3. Best practices\n4. Common pitfalls\n\nDocumentation:`,
        },
      ];

      const result = await this.hf.chatCompletion({
        model: compatibleModel,
        messages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        provider: provider as InferenceProvider,
      });

      const generatedDocs = result.choices?.[0]?.message?.content || '';

      return {
        content: generatedDocs,
        outputData: {
          documentation: generatedDocs,
          metadata: {
            type: 'technical_documentation',
            format: 'markdown',
            model: compatibleModel,
            tokensGenerated: generatedDocs.split(' ').length,
          },
        },
        processingTime: Date.now() - startTime,
        model: compatibleModel,
      };
    } catch (error: any) {
      let errorMessage = 'Unknown error';
      let errorType = 'UnknownError';

      if (error instanceof InferenceClientProviderApiError) {
        errorMessage = `Provider API Error: ${error.message}`;
        errorType = 'InferenceClientProviderApiError';
      } else if (error instanceof InferenceClientProviderOutputError) {
        errorMessage = `Provider Output Error: ${error.message}`;
        errorType = 'InferenceClientProviderOutputError';
      } else if (error instanceof InferenceClientInputError) {
        errorMessage = `Input Error: ${error.message}`;
        errorType = 'InferenceClientInputError';
      } else if (error instanceof InferenceClientError) {
        errorMessage = `Inference Error: ${error.message}`;
        errorType = 'InferenceClientError';
      } else if (error instanceof Error) {
        errorMessage = error.message;
        errorType = error.constructor.name;
      }

      this.logger.error(`Documentation generation failed: ${errorMessage}`);

      // Fallback response
      return {
        content:
          'I apologize, but I encountered an error while generating the documentation. Please try again with a different prompt.',
        outputData: {
          documentation:
            '# Error\n\nAn error occurred during documentation generation.',
          metadata: {
            error: true,
            errorMessage,
            errorType,
          },
        },
        processingTime: Date.now() - startTime,
        model,
      };
    }
  }

  /**
   * Validate model-provider compatibility and provide fallback
   */
  private getCompatibleModelAndProvider(type: GenerationType): {
    model: string;
    provider: string;
  } {
    let model: string;
    let provider: string;

    switch (type) {
      case GenerationType.UI:
        model = this.config.uiModel || 'meta-llama/CodeLlama-7b-Instruct-hf';
        provider = this.config.uiProvider || 'hf-inference';
        break;
      case GenerationType.DOCUMENTATION:
        model =
          this.config.documentationModel || 'meta-llama/Llama-3.2-3B-Instruct';
        provider = this.config.documentationProvider || 'hf-inference';
        break;
      default:
        model = this.config.defaultModel || 'meta-llama/Llama-3.2-3B-Instruct';
        provider = this.config.defaultProvider || 'hf-inference';
    }

    if (provider === 'together' && model.includes('moonshotai')) {
      this.logger.warn(
        `Model ${model} not compatible with provider ${provider}, falling back to hf-inference`,
      );
      provider = 'hf-inference';
    }

    return { model, provider };
  }

  /**
   * Generate streaming response for chat conversations
   */
  async *generateResponseStream(
    messages: any[],
  ): AsyncGenerator<string, void, unknown> {
    this.logger.log('Generating response stream from Hugging Face');

    try {
      // Format messages to HF chat completion format
      const formattedMessages = messages.map((msg) => ({
        role: msg.role.toLowerCase(),
        content: msg.content,
      }));

      const stream = this.hf.chatCompletionStream({
        model: this.config.defaultModel!,
        messages: formattedMessages,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
      });

      for await (const chunk of stream) {
        if (chunk.choices?.[0]?.delta?.content) {
          yield chunk.choices[0].delta.content;
        }
      }
    } catch (error) {
      this.logger.error(
        'Error generating response stream:',
        this.getErrorMessage(error),
      );
      throw new InternalServerErrorException(
        'Failed to generate streaming response',
      );
    }
  }

  /**
   * Health check endpoint that includes provider configuration
   */
  healthCheck(): { status: string; models: string[]; config: any } {
    try {
      return {
        status: 'healthy',
        models: [
          this.config.defaultModel!,
          this.config.uiModel!,
          this.config.documentationModel!,
        ],
        config: {
          maxTokens: this.config.maxTokens,
          temperature: this.config.temperature,
          hasApiKey: !!this.config.apiKey,
          providers: {
            default: this.config.defaultProvider,
            ui: this.config.uiProvider,
            documentation: this.config.documentationProvider,
          },
        },
      };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        models: [],
        config: {},
      };
    }
  }
}
