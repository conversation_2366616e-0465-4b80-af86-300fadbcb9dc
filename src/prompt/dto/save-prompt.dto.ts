import {
  IsString,
  IsEnum,
  IsOptional,
  IsArray,
  IsNotEmpty,
  MinLength,
  MaxLength,
} from 'class-validator';
import { GenerationType } from 'generated/prisma';

export class SavePromptDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(1000)
  prompt: string;

  @IsEnum(GenerationType)
  type: GenerationType;

  @IsOptional()
  @IsString()
  @MaxLength(2000)
  description?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  projectId?: string;

  @IsString()
  @IsNotEmpty()
  createdById: string;
}
