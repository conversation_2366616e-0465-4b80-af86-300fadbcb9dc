import { Body, Controller, Post } from '@nestjs/common';
import { PromptService } from './prompt.service';
import { SavePromptDto } from './dto/save-prompt.dto';

@Controller('prompt')
export class PromptController {
  constructor(private readonly promptService: PromptService) {}

  @Post('save')
  async savePrompt(@Body() savePromptDto: SavePromptDto) {
    return this.promptService.savePrompt(savePromptDto);
  }
}
