import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { SavePromptDto } from './dto/save-prompt.dto';

@Injectable()
export class PromptService {
  constructor(private readonly prismaService: PrismaService) {}
  async savePrompt(savePromptDto: SavePromptDto) {
    try {
      // If projectId is provided, validate it exists
      if (savePromptDto.projectId) {
        const projectExists = await this.prismaService.project.findUnique({
          where: { id: savePromptDto.projectId },
        });

        if (!projectExists) {
          throw new NotFoundException(
            `Project with ID ${savePromptDto.projectId} not found`,
          );
        }
      }

      const prompt = await this.prismaService.promptHistory.create({
        data: savePromptDto,
      });

      return {
        message: 'Prompt saved successfully',
        data: prompt,
        statusCode: 201,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Handle Prisma foreign key constraint errors
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        (error as { code: string }).code === 'P2003'
      ) {
        throw new BadRequestException(
          'Invalid projectId or createdById provided',
        );
      }

      throw new Error(error as string);
    }
  }
}
