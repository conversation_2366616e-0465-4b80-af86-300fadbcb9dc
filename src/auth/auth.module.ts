import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { UsersService } from 'src/users/users.service';
import { PrismaService } from 'src/prisma.service';
import { JwtModule } from '@nestjs/jwt';
import { TokenService } from 'src/token/token.service';

@Module({
  imports: [
    JwtModule.register({
      global: true,
      signOptions: { expiresIn: '1h' },
      secret: process.env.JWT_SECRET || 'default_secret',
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, UsersService, PrismaService, TokenService],
})
export class AuthModule {}
