import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsO<PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class AddMessageDto {
  @ApiProperty({
    description: 'Message content to add to the conversation',
    example: 'Make the component darker and add hover effects',
    minLength: 1,
    maxLength: 2000,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(2000)
  content: string;

  @ApiPropertyOptional({
    description: 'Optional input data (code snippets, files, etc.)',
    example: {
      codeSnippet: 'const example = "code";',
      attachments: ['file1.png'],
      preferences: { theme: 'dark' },
    },
  })
  @IsOptional()
  @IsObject()
  inputData?: any;
}
