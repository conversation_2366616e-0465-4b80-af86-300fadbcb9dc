import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { GenerationStatus, GenerationType } from 'generated/prisma';

/**
 * Generation response DTO
 */
export class GenerationResponseDto {
  @ApiProperty({
    description: 'Generation ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'User-friendly name for the generation',
    example: 'Dashboard Component',
  })
  name: string;

  @ApiProperty({
    description: 'Type of generation',
    enum: GenerationType,
    example: GenerationType.UI,
  })
  type: GenerationType;

  @ApiProperty({
    description: 'Current status of the generation',
    enum: GenerationStatus,
    example: GenerationStatus.COMPLETED,
  })
  status: GenerationStatus;

  @ApiPropertyOptional({
    description: 'Initial prompt used to start the generation',
    example: 'Create a responsive dashboard with charts and metrics',
  })
  initialPrompt?: string;

  @ApiPropertyOptional({
    description: 'Current result of the generation',
    example: '<div className="dashboard">...</div>',
  })
  currentResult?: string;

  @ApiPropertyOptional({
    description: 'Final result of the generation',
    example: '<div className="dashboard">...</div>',
  })
  result?: string;

  @ApiPropertyOptional({
    description: 'Metadata about the generation',
    example: {
      model: 'meta-llama/CodeLlama-7b-Instruct-hf',
      processingTime: 2500,
      framework: 'React',
      styling: 'Tailwind CSS',
      language: 'TypeScript',
    },
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Project information',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'E-commerce Dashboard',
    },
  })
  project: {
    id: string;
    name: string;
    createdById?: string;
  };

  @ApiProperty({
    description: 'Creation timestamp',
    format: 'date-time',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    format: 'date-time',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;
}

/**
 * Conversation message response DTO
 */
export class ConversationMessageResponseDto {
  @ApiProperty({
    description: 'Message ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'Generation ID this message belongs to',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  generationId: string;

  @ApiProperty({
    description: 'Role of the message sender',
    enum: ['SYSTEM', 'USER', 'ASSISTANT'],
    example: 'USER',
  })
  role: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Make the dashboard darker with a sidebar on the left',
  })
  content: string;

  @ApiProperty({
    description: 'Message index in the conversation',
    example: 3,
  })
  messageIndex: number;

  @ApiPropertyOptional({
    description: 'Input data provided with the message',
    example: {
      codeSnippet: 'const example = "code";',
      preferences: { theme: 'dark' },
    },
  })
  inputData?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Output data generated for the message',
    example: {
      code: '<div className="dashboard dark">...</div>',
      metadata: {
        framework: 'React',
        styling: 'Tailwind CSS',
      },
    },
  })
  outputData?: Record<string, any>;

  @ApiProperty({
    description: 'Message status',
    enum: ['PENDING', 'SENT', 'COMPLETED', 'FAILED'],
    example: 'COMPLETED',
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Processing time in milliseconds',
    example: 1500,
  })
  processingTime?: number;

  @ApiPropertyOptional({
    description: 'Error message if the message failed',
    example: 'Model timeout',
  })
  errorMessage?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    format: 'date-time',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'User who created the message',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      email: '<EMAIL>',
      fullName: 'John Doe',
    },
  })
  createdBy?: {
    id: string;
    email: string;
    fullName: string;
  };
}

/**
 * Conversation history response DTO
 */
export class ConversationHistoryResponseDto {
  @ApiProperty({
    description: 'Generation information',
    type: () => ({
      id: String,
      name: String,
      type: String,
      status: String,
      project: {
        id: String,
        name: String,
      },
    }),
  })
  generation: {
    id: string;
    name: string;
    type: string;
    status: string;
    project: {
      id: string;
      name: string;
    };
  };

  @ApiProperty({
    description: 'Conversation messages',
    type: [ConversationMessageResponseDto],
  })
  messages: ConversationMessageResponseDto[];

  @ApiProperty({
    description: 'Pagination information',
    type: () => ({
      page: Number,
      limit: Number,
      total: Number,
      pages: Number,
    }),
  })
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
