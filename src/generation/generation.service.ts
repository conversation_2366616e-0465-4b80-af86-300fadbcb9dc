import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  MessageEvent,
} from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { AssistantService } from '../assistant/assistant.service';
import { StreamingService } from './streaming.service';
import {
  GenerationType,
  GenerationStatus,
  MessageRole,
  MessageStatus,
  UserRole,
} from 'generated/prisma';
import {
  AssistantRequest,
  AssistantMessage,
} from '../assistant/interfaces/assistant.interface';
import { Observable } from 'rxjs';
import { SavePayloadDto } from 'src/collaboration/dto/save-payload.dto';

@Injectable()
export class GenerationService {
  private readonly logger = new Logger(GenerationService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly assistantService: AssistantService,
    private readonly streamingService: StreamingService,
  ) {}

  async saveDocument(savePayloadDto: SavePayloadDto) {
    const { generationId } = savePayloadDto;
    await this.prismaService.generation
      .findUnique({
        where: { id: generationId },
      })
      .then((generation) => {
        if (!generation) {
          throw new NotFoundException('Generation not found');
        }

        return this.prismaService.document.update({
          where: { id: generationId },
          data: {
            content: savePayloadDto.content,
          },
        });
      })
      .catch((error) => {
        this.logger.error('Failed to save document', error);
        throw new BadRequestException('Failed to save document');
      });
    this.logger.log(`Document saved for generation ${generationId}`);
  }

  async createConversationalGeneration(
    projectId: string,
    name: string,
    type: GenerationType,
    initialPrompt: string,
    createdById: string,
  ) {
    try {
      const project = await this.prismaService.project.findFirst({
        where: {
          id: projectId,
          OR: [{ createdById }, { members: { some: { userId: createdById } } }],
        },
      });

      if (!project) {
        throw new NotFoundException('Project not found or access denied');
      }

      const result = await this.prismaService.$transaction(async (tx) => {
        const generation = await tx.generation.create({
          data: {
            name,
            type,
            prompt: initialPrompt,
            initialPrompt,
            projectId,
            createdById,
            status: GenerationStatus.PENDING,
          },
        });

        await tx.conversationMessage.create({
          data: {
            generationId: generation.id,
            role: MessageRole.SYSTEM,
            content: `Starting ${type.toLowerCase()} generation: ${name}`,
            messageIndex: 0,
            status: MessageStatus.SENT,
          },
        });

        // Create initial user message
        await tx.conversationMessage.create({
          data: {
            generationId: generation.id,
            role: MessageRole.USER,
            content: initialPrompt,
            messageIndex: 1,
            createdById,
            status: MessageStatus.SENT,
          },
        });

        return generation;
      });

      this.logger.log(
        `Conversational generation created: ${result.id} for project ${projectId}`,
      );

      // Process initial AI response asynchronously
      void this.processInitialGeneration(
        result.id,
        type,
        initialPrompt,
        projectId,
      );

      return {
        message: 'Conversational generation created successfully',
        data: result,
        statusCode: 201,
      };
    } catch (error) {
      this.logger.error('Failed to create conversational generation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create conversational generation',
      );
    }
  }

  async addMessageToConversation(
    generationId: string,
    content: string,
    createdById: string,
    inputData?: any,
  ) {
    try {
      // Validate generation exists and user has access
      const generation = await this.prismaService.generation.findFirst({
        where: {
          id: generationId,
          project: {
            OR: [
              { createdById },
              { members: { some: { userId: createdById } } },
            ],
          },
        },
        include: {
          project: true,
        },
      });

      if (!generation) {
        throw new NotFoundException('Generation not found or access denied');
      }

      // Get next message index
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      const userMessage = await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.USER,
          content,
          messageIndex: nextIndex,
          createdById,
          inputData,
          status: MessageStatus.SENT,
        },
      });

      this.logger.log(
        `Message added to conversation ${generationId}: ${userMessage.id}`,
      );

      // Process AI response asynchronously
      void this.processConversationMessage(
        generationId,
        generation.type,
        generation.project.id,
      );

      return {
        message: 'Message added to conversation successfully',
        data: userMessage,
        statusCode: 201,
      };
    } catch (error) {
      this.logger.error('Failed to add message to conversation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to add message to conversation');
    }
  }

  async getConversationHistory(
    generationId: string,
    userId: string,
    userRole: UserRole,
    page: number = 1,
    limit: number = 50,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      const skip = (page - 1) * limit;

      const [messages, totalCount] = await Promise.all([
        this.prismaService.conversationMessage.findMany({
          where: { generationId },
          include: {
            createdBy: {
              select: {
                id: true,
                email: true,
                fullName: true,
              },
            },
          },
          orderBy: { messageIndex: 'asc' },
          skip,
          take: limit,
        }),
        this.prismaService.conversationMessage.count({
          where: { generationId },
        }),
      ]);

      return {
        message: 'Conversation history retrieved successfully',
        data: {
          generation: {
            id: generation.id,
            name: generation.name,
            type: generation.type,
            status: generation.status,
            project: generation.project,
          },
          messages,
          pagination: {
            page,
            limit,
            total: totalCount,
            pages: Math.ceil(totalCount / limit),
          },
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get conversation history', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get conversation history');
    }
  }

  async getCurrentResult(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      return {
        message: 'Current result retrieved successfully',
        data: {
          id: generation.id,
          name: generation.name,
          type: generation.type,
          status: generation.status,
          result: generation.result,
          currentResult: generation.currentResult,
          metadata: generation.metadata,
          project: generation.project,
          createdAt: generation.createdAt,
          updatedAt: generation.updatedAt,
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get current result', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get current result');
    }
  }

  private async processInitialGeneration(
    generationId: string,
    type: GenerationType,
    initialPrompt: string,
    projectId: string,
  ) {
    try {
      // Update generation status to IN_PROGRESS
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: { status: GenerationStatus.IN_PROGRESS },
      });

      // Get project context
      const project = await this.prismaService.project.findUnique({
        where: { id: projectId },
        select: { name: true, description: true },
      });

      // Prepare the initial user message with project context
      const contextualPrompt = `Project: ${project?.name} - ${project?.description}\n\n${initialPrompt}`;

      // Use streaming service to process the response and emit events
      await this.streamingService.processStreamingResponse(
        generationId,
        contextualPrompt,
      );

      // Update generation status to completed
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: { status: GenerationStatus.COMPLETED },
      });

      this.logger.log(`Initial generation completed for ${generationId}`);
    } catch (error) {
      this.logger.error(
        `Error processing initial generation for ${generationId}:`,
        error,
      );

      // Update generation status to failed
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: { status: GenerationStatus.FAILED },
      });

      throw error;
    }
  }

  private async processConversationMessage(
    generationId: string,
    type: GenerationType,
    projectId: string,
  ) {
    try {
      // Get conversation history
      const messages = await this.prismaService.conversationMessage.findMany({
        where: { generationId },
        orderBy: { messageIndex: 'asc' },
        include: {
          createdBy: {
            select: { id: true, fullName: true },
          },
        },
      });

      // Convert to assistant message format
      const conversationHistory: AssistantMessage[] = messages
        .filter((msg) => msg.role !== MessageRole.SYSTEM)
        .map((msg) => ({
          role: msg.role,
          content: msg.content,
          inputData: msg.inputData,
          outputData: msg.outputData,
        }));

      // Get the latest user message
      const latestUserMessage = messages
        .filter((msg) => msg.role === MessageRole.USER)
        .pop();

      if (!latestUserMessage) {
        throw new Error('No user message found');
      }

      // Get project context
      const project = await this.prismaService.project.findUnique({
        where: { id: projectId },
        select: { name: true, description: true },
      });

      // Prepare assistant request
      const assistantRequest: AssistantRequest = {
        generationType: type,
        conversationHistory: conversationHistory.slice(0, -1), // Exclude the latest message
        userMessage: latestUserMessage.content,
        inputData: latestUserMessage.inputData,
        metadata: {
          projectContext: `Project: ${project?.name} - ${project?.description}`,
        },
      };

      // Get AI response
      const assistantResponse =
        await this.assistantService.processRequest(assistantRequest);

      // Get next message index
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      // Save assistant response
      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content: assistantResponse.content,
          messageIndex: nextIndex,
          outputData: assistantResponse.outputData,
          status: MessageStatus.COMPLETED,
          processingTime: assistantResponse.processingTime,
        },
      });

      // Update generation with latest result
      await this.prismaService.generation.update({
        where: { id: generationId },
        data: {
          result:
            assistantResponse.outputData?.code || assistantResponse.content,
          currentResult:
            assistantResponse.outputData?.code || assistantResponse.content,
          metadata: {
            ...assistantResponse.outputData?.metadata,
            model: assistantResponse.model,
            lastProcessingTime: assistantResponse.processingTime,
          },
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Conversation message processed for ${generationId}`);
    } catch (error) {
      this.logger.error('Failed to process conversation message', error);

      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: { generationId },
          orderBy: { messageIndex: 'desc' },
        });

      const nextIndex = (lastMessage?.messageIndex || 0) + 1;

      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content:
            'I apologize, but I encountered an error while processing your message. Please try again.',
          messageIndex: nextIndex,
          status: MessageStatus.FAILED,
          errorMessage:
            error instanceof Error ? error.message : 'Unknown error',
        },
      });
    }
  }

  private async validateGenerationAccess(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    const generation = await this.prismaService.generation.findUnique({
      where: { id: generationId },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            createdById: true,
          },
        },
      },
    });

    if (!generation) {
      throw new NotFoundException('Generation not found');
    }

    // Check access permissions
    if (userRole === UserRole.ADMIN) {
      return generation;
    }

    if (generation.project.createdById === userId) {
      return generation;
    }

    const membership = await this.prismaService.projectMember.findFirst({
      where: {
        projectId: generation.project.id,
        userId: userId,
      },
    });

    if (!membership) {
      throw new NotFoundException('Generation not found or access denied');
    }

    return generation;
  }

  async listGenerationsForProject(
    projectId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generations = await this.prismaService.generation.findMany({
        where: {
          projectId,
          ...(userRole !== UserRole.ADMIN && {
            project: {
              OR: [
                { createdById: userId },
                { members: { some: { userId: userId } } },
              ],
            },
          }),
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Generations listed successfully',
        data: generations,
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to list generations for project', error);
      throw new BadRequestException('Failed to list generations');
    }
  }

  async getAllGenerations(userId: string, userRole: UserRole) {
    try {
      let whereClause: any = {};

      // If user is not an admin, only show their own and project generations
      if (userRole !== UserRole.ADMIN) {
        whereClause = {
          project: {
            OR: [{ createdById: userId }, { members: { some: { userId } } }],
          },
        };
      }

      const generations = await this.prismaService.generation.findMany({
        where: whereClause,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              createdById: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Generations retrieved successfully',
        data: generations,
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get all generations:', error);
      throw new BadRequestException('Failed to retrieve generations');
    }
  }

  async getGenerationById(
    generationId: string,
    userId: string,
    userRole: UserRole,
  ) {
    try {
      const generation = await this.validateGenerationAccess(
        generationId,
        userId,
        userRole,
      );

      return {
        message: 'Generation retrieved successfully',
        data: {
          id: generation.id,
          name: generation.name,
          type: generation.type,
          prompt: generation.prompt,
          initialPrompt: generation.initialPrompt,
          status: generation.status,
          result: generation.result,
          currentResult: generation.currentResult,
          metadata: generation.metadata,
          project: generation.project,
          createdAt: generation.createdAt,
          updatedAt: generation.updatedAt,
        },
        statusCode: 200,
      };
    } catch (error) {
      this.logger.error('Failed to get generation', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get generation');
    }
  }

  streamGenerationEvents(generationId: string): Observable<MessageEvent> {
    return new Observable((observer) => {
      let pollInterval: NodeJS.Timeout;
      let heartbeatInterval: NodeJS.Timeout;
      let lastGenerationState: any = null;
      const connectionId = `${generationId}-${Date.now()}`;

      this.logger.log(`SSE connection opened: ${connectionId}`);

      const sendEvent = (type: string, data: any) => {
        const event = {
          type,
          generationId,
          timestamp: new Date().toISOString(),
          data,
        };

        observer.next({
          data: JSON.stringify(event),
        } as MessageEvent);
      };

      const sendErrorEvent = (error: string, details?: any) => {
        sendEvent('error', {
          status: lastGenerationState?.status || 'UNKNOWN',
          result: lastGenerationState?.currentResult || null,
          progress: null,
          metadata: lastGenerationState?.metadata || null,
          error,
          details,
        });
      };

      const checkGeneration = async () => {
        try {
          const generation = await this.prismaService.generation.findUnique({
            where: { id: generationId },
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  createdById: true,
                },
              },
            },
          });

          if (!generation) {
            sendErrorEvent('Generation not found');
            return;
          }

          // Check if generation state has changed
          const hasChanged =
            !lastGenerationState ||
            lastGenerationState.status !== generation.status ||
            lastGenerationState.currentResult !== generation.currentResult ||
            JSON.stringify(lastGenerationState.metadata) !==
              JSON.stringify(generation.metadata);

          if (hasChanged) {
            lastGenerationState = generation;

            // Determine event type based on status
            let eventType = 'status';
            if (generation.status === GenerationStatus.IN_PROGRESS) {
              eventType = 'progress';
            } else if (generation.status === GenerationStatus.COMPLETED) {
              eventType = 'completed';
            } else if (generation.status === GenerationStatus.FAILED) {
              eventType = 'failed';
            }

            // Calculate progress (simple estimation based on status)
            let progress: number | null = null;
            if (generation.status === GenerationStatus.PENDING) progress = 0;
            else if (generation.status === GenerationStatus.IN_PROGRESS)
              progress = 50;
            else if (generation.status === GenerationStatus.COMPLETED)
              progress = 100;
            else if (generation.status === GenerationStatus.FAILED)
              progress = 0;

            sendEvent(eventType, {
              status: generation.status,
              result: generation.currentResult,
              progress,
              metadata: {
                ...(generation.metadata &&
                typeof generation.metadata === 'object'
                  ? generation.metadata
                  : {}),
                name: generation.name,
                projectId: generation.projectId,
                projectName: generation.project.name,
                createdAt: generation.createdAt,
                updatedAt: generation.updatedAt,
              },
              error:
                generation.status === GenerationStatus.FAILED
                  ? 'Generation failed'
                  : null,
            });
          }
        } catch (error) {
          this.logger.error(
            `Error checking generation ${generationId}:`,
            error,
          );
          sendErrorEvent(
            'Failed to check generation status',
            error instanceof Error ? error.message : 'Unknown error',
          );
        }
      };

      const startStreaming = async () => {
        try {
          // Send initial state immediately
          await checkGeneration();

          // Start polling for changes every 2 seconds
          pollInterval = setInterval(checkGeneration, 2000);

          // Start heartbeat every 30 seconds
          heartbeatInterval = setInterval(() => {
            sendEvent('heartbeat', {
              status: lastGenerationState?.status || 'UNKNOWN',
              result: lastGenerationState?.currentResult || null,
              progress: null,
              metadata: {
                connectionId,
                uptime: Date.now() - parseInt(connectionId.split('-')[1]),
              },
              error: null,
            });
          }, 30000);
        } catch (error) {
          this.logger.error(
            `Error starting stream for ${generationId}:`,
            error,
          );
          sendErrorEvent(
            'Failed to start generation stream',
            error instanceof Error ? error.message : 'Unknown error',
          );
        }
      };

      // Start the streaming process
      void startStreaming();

      // Cleanup function when client disconnects - NEVER auto-complete
      return () => {
        this.logger.log(`SSE connection closed: ${connectionId}`);

        if (pollInterval) {
          clearInterval(pollInterval);
        }

        if (heartbeatInterval) {
          clearInterval(heartbeatInterval);
        }
      };
    });
  }
}
