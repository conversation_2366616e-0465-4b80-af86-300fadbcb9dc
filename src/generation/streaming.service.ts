import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { AssistantService } from '../assistant/assistant.service';
import { MessageRole, MessageStatus } from 'generated/prisma';
import { Server } from 'socket.io';

@Injectable()
export class StreamingService {
  private server: Server | null = null;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly assistantService: AssistantService,
  ) {}

  /**
   * Set the Socket.IO server instance
   */
  setServer(server: Server) {
    this.server = server;
  }

  /**
   * Process a streaming response for a generation and emit events to room
   * @param generationId The generation ID
   * @param prompt The prompt to process
   * @returns The ID of the assistant message
   */
  async processStreamingResponse(
    generationId: string,
    prompt: string,
  ): Promise<string> {
    if (!this.server) {
      throw new Error('Socket server not initialized');
    }

    const roomName = `generation:${generationId}`;

    // Create pending assistant message
    const lastMessage = await this.prismaService.conversationMessage.findFirst({
      where: { generationId },
      orderBy: { messageIndex: 'desc' },
    });

    const nextIndex = (lastMessage?.messageIndex || 0) + 1;

    const assistantMessage =
      await this.prismaService.conversationMessage.create({
        data: {
          generationId,
          role: MessageRole.ASSISTANT,
          content: '',
          messageIndex: nextIndex,
          status: MessageStatus.PROCESSING,
        },
      });

    try {
      // Emit stream start to all clients in room
      this.server.to(roomName).emit('chat:stream_start', {
        messageId: assistantMessage.id,
        generationId,
      });

      // Get messages for context
      const messages = await this.prismaService.conversationMessage.findMany({
        where: { generationId },
        orderBy: { messageIndex: 'asc' },
      });

      // Add the new prompt as a user message to the context
      const contextMessages = [
        ...messages.map((msg) => ({
          role: msg.role,
          content: msg.content,
        })),
        {
          role: MessageRole.USER,
          content: prompt,
        },
      ];

      // Process the streaming response using assistant service
      const responseStream =
        this.assistantService.generateResponseStream(contextMessages);

      let fullContent = '';

      for await (const chunk of responseStream) {
        if (chunk) {
          fullContent += chunk;

          // Emit streaming response to all clients in room
          this.server.to(roomName).emit('chat:response', {
            messageId: assistantMessage.id,
            content: chunk,
            isStream: true,
          });
        }
      }

      // Update message with final content and mark as completed
      await this.prismaService.conversationMessage.update({
        where: { id: assistantMessage.id },
        data: {
          content: fullContent,
          status: MessageStatus.COMPLETED,
        },
      });

      // Emit stream end
      this.server.to(roomName).emit('chat:stream_end', {
        messageId: assistantMessage.id,
        generationId,
      });

      return assistantMessage.id;
    } catch (error) {
      console.error('Error processing streaming response:', error);

      // Mark message as failed
      await this.prismaService.conversationMessage.update({
        where: { id: assistantMessage.id },
        data: {
          content: 'Error processing request',
          status: MessageStatus.COMPLETED,
        },
      });

      // Emit error to room
      this.server.to(roomName).emit('chat:error', {
        messageId: assistantMessage.id,
        error: 'Failed to process message',
      });

      throw error;
    }
  }
}
