import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  WebSocketServer,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { GenerationService } from './generation.service';
import { StreamingService } from './streaming.service';
import { PrismaService } from '../prisma.service';
import { Logger } from '@nestjs/common';
import { MessageRole, MessageStatus, UserRole } from 'generated/prisma';
import { AuthenticatedSocket } from '../auth/interfaces/authenticated-socket.interface';

@WebSocketGateway({ namespace: 'chat' })
export class ChatGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  private readonly logger = new Logger(ChatGateway.name);

  @WebSocketServer()
  server: Server;

  constructor(
    private readonly generationService: GenerationService,
    private readonly streamingService: StreamingService,
    private readonly prismaService: PrismaService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');
    this.logger.debug(`Server adapter ready: ${!!server.sockets?.adapter}`);

    // Set server instance in StreamingService for shared use
    this.streamingService.setServer(this.server);
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);

    // Diagnostic logging
    this.logger.debug(`Server object exists: ${!!this.server}`);
    this.logger.debug(`Server.sockets exists: ${!!this.server?.sockets}`);
    this.logger.debug(
      `Server.sockets.adapter exists: ${!!this.server?.sockets?.adapter}`,
    );

    // Only warn if server is still not ready after a reasonable time
    setTimeout(() => {
      if (!this.server?.sockets?.adapter) {
        this.logger.warn(
          'WebSocket server initialization taking longer than expected',
        );
      }
    }, 1000);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  private getRoomSize(roomName: string): number {
    try {
      // If server adapter is not ready, return 1 (assuming the current client)
      if (!this.server?.sockets?.adapter) {
        this.logger.debug(
          `Server adapter not ready, returning default room size for ${roomName}`,
        );
        return 1;
      }
      return this.server.sockets.adapter.rooms.get(roomName)?.size || 0;
    } catch (error) {
      this.logger.warn(
        `Failed to get room size for ${roomName}: ${error.message}`,
      );
      return 1; // Return 1 as fallback (current client)
    }
  }

  private async waitForServerReady(maxAttempts: number = 5): Promise<boolean> {
    for (let i = 0; i < maxAttempts; i++) {
      if (this.server?.sockets?.adapter) {
        this.logger.debug('WebSocket server adapter is ready');
        return true;
      }
      if (i < maxAttempts - 1) {
        // Don't log on last attempt
        this.logger.debug(
          `Waiting for server initialization (attempt ${i + 1}/${maxAttempts})`,
        );
      }
      await new Promise((resolve) => setTimeout(resolve, 200)); // Wait 200ms
    }
    this.logger.warn(
      'WebSocket server adapter not ready, proceeding with limited functionality',
    );
    return false; // Proceed anyway with limited functionality
  }

  @SubscribeMessage('chat:join')
  async handleJoinConversation(
    @MessageBody() data: { generationId: string; userId: string },
    @ConnectedSocket() client: Socket,
  ) {
    this.logger.log(
      `Processing join request for generation ${data.generationId} from user ${data.userId || 'unknown'}`,
    );

    try {
      // Validate required data
      if (!data.generationId) {
        throw new Error('Generation ID is required');
      }
      if (!data.userId) {
        this.logger.warn(
          'User ID not provided in join request, using anonymous mode',
        );
      }

      // Wait for server to be ready (or proceed with limited functionality)
      await this.waitForServerReady();
      // Continue regardless of server readiness for basic functionality

      // Join the client to a room for this specific generation
      await client.join(`generation:${data.generationId}`);

      // Get generation status and participant count
      const generation = await this.prismaService.generation.findUnique({
        where: { id: data.generationId },
        select: { status: true, type: true },
      });

      const roomSize = this.getRoomSize(`generation:${data.generationId}`);

      // Load and send conversation history
      const conversation = await this.generationService.getConversationHistory(
        data.generationId,
        data.userId || 'anonymous', // Provide fallback for undefined userId
        UserRole.USER,
      );

      // Send the complete conversation history and room status to the client
      client.emit('chat:joined', {
        generationId: data.generationId,
        messages: conversation.data.messages,
        pagination: conversation.data.pagination,
        generationStatus: generation?.status || 'UNKNOWN',
        participantCount: roomSize,
      });

      // Also send legacy chat:history for backward compatibility
      client.emit('chat:history', {
        generationId: data.generationId,
        messages: conversation.data.messages,
        pagination: conversation.data.pagination,
      });

      this.logger.log(
        `Client ${client.id} joined conversation for generation ${data.generationId} (${roomSize} participants)`,
      );
    } catch (error) {
      this.logger.error(
        `Error loading conversation history for generation ${data.generationId}: ${error.message}`,
      );
      client.emit('chat:error', {
        message: 'Failed to load conversation history.',
        generationId: data.generationId,
      });
    }
  }

  @SubscribeMessage('chat:leave')
  async handleLeaveConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() { generationId }: { generationId: string },
  ) {
    try {
      await client.leave(`generation:${generationId}`);

      // Get updated room size
      const roomSize = this.getRoomSize(`generation:${generationId}`);

      // Notify remaining users in the room
      this.server.to(`generation:${generationId}`).emit('chat:user_left', {
        user: {
          id: client.user?.sub || '',
          email: client.user?.email || '',
        },
        participantCount: roomSize,
      });
    } catch {
      client.emit('chat:error', { message: 'Failed to leave conversation' });
    }
  }

  @SubscribeMessage('chat:request_stream')
  async handleRequestStream(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() { generationId }: { generationId: string },
  ) {
    try {
      // Check if there's an active stream for this generation
      const lastMessage =
        await this.prismaService.conversationMessage.findFirst({
          where: {
            generationId,
            role: MessageRole.ASSISTANT,
          },
          orderBy: { createdAt: 'desc' },
        });

      if (lastMessage && lastMessage.status === MessageStatus.PROCESSING) {
        // There's an active stream, emit the partial content to the requesting client
        client.emit('chat:stream_recovery', {
          messageId: lastMessage.id,
          content: lastMessage.content,
          isComplete: false,
        });
      } else {
        // No active stream
        client.emit('chat:stream_recovery', {
          messageId: null,
          content: '',
          isComplete: true,
        });
      }
    } catch {
      client.emit('chat:error', { message: 'Failed to request stream' });
    }
  }

  @SubscribeMessage('chat:message')
  async handleMessage(
    @MessageBody()
    data: {
      generationId: string;
      text: string;
      userId: string;
    },
  ) {
    this.logger.log(
      `Received message for generation ${data.generationId} from user ${data.userId}`,
    );

    try {
      // Add user message to conversation
      await this.generationService.addMessageToConversation(
        data.generationId,
        data.text,
        data.userId,
      );

      // Broadcast the user message to all clients in this conversation
      this.server
        .to(`generation:${data.generationId}`)
        .emit('chat:user_message', {
          generationId: data.generationId,
          content: data.text,
          userId: data.userId,
          timestamp: new Date().toISOString(),
        });

      // Use StreamingService to handle AI response with WebSocket events
      await this.streamingService.processStreamingResponse(
        data.generationId,
        data.text,
      );
    } catch (error) {
      this.logger.error(
        `Error handling message for generation ${data.generationId}: ${error.message}`,
      );

      // Send error to all clients in the conversation
      this.server.to(`generation:${data.generationId}`).emit('chat:error', {
        generationId: data.generationId,
        message: 'An error occurred while processing the message.',
      });
    }
  }
}
