import { Test, TestingModule } from '@nestjs/testing';
import { GenerationGateway } from './generation.gateway';

describe('GenerationGateway', () => {
  let gateway: GenerationGateway;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [GenerationGateway],
    }).compile();

    gateway = module.get<GenerationGateway>(GenerationGateway);
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });
});
