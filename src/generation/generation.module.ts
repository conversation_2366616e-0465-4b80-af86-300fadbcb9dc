import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { GenerationService } from './generation.service';
import { StreamingService } from './streaming.service';
import { PrismaService } from '../prisma.service';
import { AssistantModule } from '../assistant/assistant.module';
import {
  GenerationController,
  ProjectGenerationController,
} from './generation.controller';
import { GenerationGateway } from './generation.gateway';
import { ChatGateway } from './chat.gateway';

@Module({
  imports: [
    AssistantModule,
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default_secret',
      signOptions: { expiresIn: '1h' },
    }),
  ],
  controllers: [GenerationController, ProjectGenerationController],
  providers: [
    GenerationService,
    StreamingService,
    PrismaService,
    GenerationGateway,
    ChatGateway,
  ],
  exports: [GenerationService, StreamingService],
})
export class GenerationModule {}
