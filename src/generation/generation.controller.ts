import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { GenerationService } from './generation.service';
import { CreateConversationalGenerationDto } from './dto/create-conversational-generation.dto';
import { AddMessageDto } from './dto/add-message.dto';
import { GetConversationDto } from './dto/get-conversation.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { AuthenticatedRequest } from '../auth/interfaces/authenticated-request.interface';

@ApiTags('generations')
@ApiBearerAuth()
@Controller('generations')
@UseGuards(AuthGuard, RolesGuard)
export class GenerationController {
  constructor(private readonly generationService: GenerationService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all generations',
    description: 'Retrieve all generations for the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Generations retrieved successfully',
  })
  async getAllGenerations(@Request() req: AuthenticatedRequest) {
    return await this.generationService.getAllGenerations(
      req.user.sub,
      req.user.role,
    );
  }

  @Post('conversational')
  @ApiOperation({
    summary: 'Create a new conversational generation',
    description: 'Creates a new generation with conversational memory support',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Conversational generation created successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Project not found or access denied',
  })
  async createConversationalGeneration(
    @Body() createDto: CreateConversationalGenerationDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.createConversationalGeneration(
      createDto.projectId,
      createDto.name,
      createDto.type,
      createDto.initialPrompt,
      req.user.sub,
    );
  }

  @Post(':id/messages')
  @ApiOperation({
    summary: 'Add a message to conversation',
    description: 'Adds a new message to an existing conversational generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Message added to conversation successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async addMessageToConversation(
    @Param('id') generationId: string,
    @Body() addMessageDto: AddMessageDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.addMessageToConversation(
      generationId,
      addMessageDto.content,
      req.user.sub,
      addMessageDto.inputData,
    );
  }

  @Get(':id/conversation')
  @ApiOperation({
    summary: 'Get conversation history',
    description: 'Retrieves the conversation history for a generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Conversation history retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getConversationHistory(
    @Param('id') generationId: string,
    @Query() query: GetConversationDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getConversationHistory(
      generationId,
      req.user.sub,
      req.user.role,
      query.page,
      query.limit,
    );
  }

  @Get(':id/result')
  @ApiOperation({
    summary: 'Get current generation result',
    description: 'Retrieves the current result of a generation',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Current result retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getCurrentResult(
    @Param('id') generationId: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getCurrentResult(
      generationId,
      req.user.sub,
      req.user.role,
    );
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get generation by ID',
    description: 'Retrieves a generation by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Generation ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Generation retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Generation not found or access denied',
  })
  async getGenerationById(
    @Param('id') generationId: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.getGenerationById(
      generationId,
      req.user.sub,
      req.user.role,
    );
  }
}

// Additional controller for project-based generation endpoints
@ApiTags('projects')
@ApiBearerAuth()
@Controller('projects')
@UseGuards(AuthGuard, RolesGuard)
export class ProjectGenerationController {
  constructor(private readonly generationService: GenerationService) {}

  @Post(':projectId/generations/conversational')
  @ApiOperation({
    summary: 'Create conversational generation for project',
    description:
      'Creates a new conversational generation within a specific project',
  })
  @ApiParam({
    name: 'projectId',
    description: 'Project ID',
    format: 'uuid',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Conversational generation created successfully',
  })
  async createProjectConversationalGeneration(
    @Param('projectId') projectId: string,
    @Body() createDto: Omit<CreateConversationalGenerationDto, 'projectId'>,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.generationService.createConversationalGeneration(
      projectId,
      createDto.name,
      createDto.type,
      createDto.initialPrompt,
      req.user.sub,
    );
  }
}
