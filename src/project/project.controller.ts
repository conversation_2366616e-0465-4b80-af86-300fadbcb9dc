import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
} from '@nestjs/common';
import { ProjectService } from './project.service';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { AddProjectMemberDto } from './dto/add-project-member.dto';
import { RemoveProjectMemberDto } from './dto/remove-project-member.dto';
import { AuthenticatedRequest } from 'src/auth/interfaces/authenticated-request.interface';

@Controller('projects')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  @Roles(['ADMIN'])
  @Post()
  async createProject(
    @Body() createProjectDto: CreateProjectDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.createProject(createProjectDto, req.user.sub);
  }

  @Get()
  async getAllProjects(@Request() req: AuthenticatedRequest) {
    return this.projectService.getAllProjects(req.user.sub, req.user.role);
  }

  @Get(':id')
  async getProjectById(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.getProjectById(id, req.user.sub, req.user.role);
  }

  @Roles(['ADMIN'])
  @Put(':id')
  async updateProject(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.updateProject(
      id,
      updateProjectDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Roles(['ADMIN'])
  @Post(':id/members')
  async addProjectMembers(
    @Param('id') id: string,
    @Body() addMembersDto: AddProjectMemberDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.addProjectMembers(
      id,
      addMembersDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Roles(['ADMIN'])
  @Delete(':id/members')
  async removeProjectMembers(
    @Param('id') id: string,
    @Body() removeMembersDto: RemoveProjectMemberDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.removeProjectMembers(
      id,
      removeMembersDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Roles(['ADMIN'])
  @Delete(':id')
  async deleteProject(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.projectService.deleteProject(id, req.user.sub, req.user.role);
  }
}
