import { Module } from '@nestjs/common';
import { InvitationService } from './invitation.service';
import { InvitationController } from './invitation.controller';
import { PrismaService } from 'src/prisma.service';
import { TokenService } from 'src/token/token.service';

import { UsersService } from 'src/users/users.service';

@Module({
  providers: [InvitationService, PrismaService, TokenService, UsersService],
  controllers: [InvitationController],
  exports: [InvitationService],
})
export class InvitationModule {}
