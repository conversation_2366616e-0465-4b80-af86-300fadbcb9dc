import { Context, Template } from '../interfaces/template';

export interface InvitationContext extends Context {
  email: string;
  token: string;
}

export const invitationTemplate = (context: InvitationContext): Template => {
  return {
    context,
    html: `
      <html>
        <body style="font-family: Arial, sans-serif; background:#f9f9f9; margin:0; padding:20px;">
          <div style="max-width:600px; margin:auto; background:#fff; padding:20px; border-radius:8px;">
            <h1 style="color:#333; font-size:24px;">You are invited to join PixiGenerator!</h1>
            <p style="color:#555; font-size:16px;">
              Click the button below to accept your invitation and create your account.
            </p>
            <a href="http://localhost:3001/register?invitation=${context.token}" style="display:inline-block; padding:10px 20px; background-color:#007bff; color:#fff; text-decoration:none; border-radius:5px;">Accept Invitation</a>
            <p style="color:#555; font-size:14px; margin-top:20px;">
              If you did not request this invitation, please ignore this email.
            </p>
          </div>
        </body>
      </html>
    `,
  };
};
