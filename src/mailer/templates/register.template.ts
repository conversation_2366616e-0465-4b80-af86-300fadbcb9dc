import { Context, Template } from '../interfaces/template';

export interface RegisterContext extends Context {
  username: string;
}

export const registerTemplate = (context: RegisterContext): Template => {
  return {
    context,
    html: `
      <html>
        <body style="font-family: Arial, sans-serif; background:#f9f9f9; margin:0; padding:20px;">
          <div style="max-width:600px; margin:auto; background:#fff; padding:20px; border-radius:8px;">
            <h1 style="color:#333; font-size:24px;">Welcome to PixiGenerator, ${context.username}!</h1>
            <p style="color:#555; font-size:16px;">
              Thank you for registering. An admin will review your account shortly.
            </p>
          </div>
        </body>
      </html>
    `,
  };
};

export const registerFromInvitation = (context: RegisterContext) => {
  return {
    context,
    html: `
      <html>
        <body style="font-family: Arial, sans-serif; background:#f9f9f9; margin:0; padding:20px;">
          <div style="max-width:600px; margin:auto; background:#fff; padding:20px; border-radius:8px;">
            <h1 style="color:#333; font-size:24px;">Welcome to PixiGenerator, ${context.username}!</h1>
            <p style="color:#555; font-size:16px;">
              Thank you for accepting our invitation and registering. Your account is now active and ready to use.
            </p>
            <p style="color:#555; font-size:16px;">
              You can now start creating amazing content with PixiGenerator!
            </p>
          </div>
        </body>
      </html>
    `,
  };
};
