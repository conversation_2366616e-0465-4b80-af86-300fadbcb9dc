import { DynamicModule, Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MailerService } from './mailer.service';
import { MAILER_OPTIONS } from './constants/mailer.constant';
import { Options } from 'nodemailer/lib/smtp-connection';
@Global()
@Module({})
export class MailerModule {
  static forRoot(options: Options): DynamicModule {
    return {
      module: MailerModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: MAILER_OPTIONS,
          useValue: options,
        },
        MailerService,
      ],
      exports: [MailerService],
    };
  }
}
