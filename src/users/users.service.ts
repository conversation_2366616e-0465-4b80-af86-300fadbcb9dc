import {
  ConflictException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import * as bcrypt from 'bcrypt';
import { UpdateUserDto } from './dto/update-user.dto';
import { MailerService } from 'src/mailer/mailer.service';
import {
  registerFromInvitation,
  registerTemplate,
} from 'src/mailer/templates/register.template';
import { UpdateUserRoleDto } from 'src/users/dto/update-user-role.dto';
import { UpdateUserStatusDto } from 'src/users/dto/update-user-status.dto';
import { GetUsersQueryDto } from 'src/users/dto/get-users-query.dto';
import { UserStatus, UserRole } from 'generated/prisma';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);
  constructor(
    private prisma: PrismaService,
    private mailerService: MailerService,
  ) {}

  // Méthode pour trouver un utilisateur par son email
  async findOne(email: string) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      return user; // Return null if not found, don't throw error
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException('Failed to find user');
    }
  }

  // Méthode pour créer un nouvel utilisateur
  async createUser(createUserDto: CreateUserDto) {
    try {
      // Vérification de l'existence de l'utilisateur
      const existingUser = await this.prisma.user.findUnique({
        where: { email: createUserDto.email },
      });
      // Si l'utilisateur existe déjà, on lance une erreur
      if (existingUser) {
        throw new ConflictException(
          `User with email ${createUserDto.email} already exists`,
        );
      }
      // Hachage du mot de passe
      const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
      // Création de l'utilisateur
      const user = await this.prisma.user.create({
        data: {
          email: createUserDto.email,
          fullName: createUserDto.fullName, // Utilisation de fullName au lieu de name
          passwordHash: hashedPassword, // Stockage du mot de passe haché
          role: createUserDto.role || 'USER', // Valeur par défaut pour le rôle
          status: createUserDto.status || UserStatus.PENDING, // Valeur par défaut pour le statut
        },
        // Sélection des champs à retourner
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      try {
        await this.mailerService.sendMail({
          to: user.email,
          subject: 'Welcome to PixiGenerator',
          template:
            user.status === UserStatus.PENDING
              ? registerTemplate({
                  username: user.fullName as string,
                  verificationLink: `https://example.com/verify?token=${user.id}`,
                })
              : registerFromInvitation({
                  username: user.fullName as string,
                }),
        });
      } catch (error: unknown) {
        this.logger.error(
          `Failed to send welcome email: ${(error as Error).message}`,
        );
      }

      this.logger.log(
        `User created successfully with email: ${createUserDto.email}`,
      );

      return {
        message: 'User created successfully',
        data: user,
        statusCode: HttpStatus.CREATED,
      };
    } catch (error: unknown) {
      this.logger.error(`Error creating user: ${(error as Error).message}`);
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to create user with email ${createUserDto.email}`,
      );
    }
  }

  // Méthode pour mettre à jour un utilisateur
  async updateUser(
    id: string,
    updateUserDto: UpdateUserDto,
    requestingUserId?: string,
    requestingUserRole?: string,
  ) {
    try {
      // Vérification de l'existence de l'utilisateur
      const existingUser = await this.prisma.user.findUnique({
        where: { id },
      });
      // Si l'utilisateur n'existe pas, on lance une erreur
      if (!existingUser) {
        throw new NotFoundException(`User with id ${id} does not exist`);
      }

      // Check permissions if requesting user info is provided
      if (requestingUserId && requestingUserRole) {
        if (requestingUserRole !== 'ADMIN' && requestingUserId !== id) {
          throw new ForbiddenException('You can only update your own profile');
        }
      }

      // Mise à jour de l'utilisateur
      const updatedUser = await this.prisma.user.update({
        where: { id },
        data: {
          ...updateUserDto,
          passwordHash: updateUserDto.password
            ? await bcrypt.hash(updateUserDto.password, 10)
            : undefined, // Hachage du mot de passe si fourni
        },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      return {
        message: 'User updated successfully',
        data: updatedUser,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(`Failed to update user: ${(error as Error).message}`);
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to update user with id ${id}`,
      );
    }
  }

  async getAllUsers(
    query: GetUsersQueryDto,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      const page = Number(query.page) || 1;
      const limit = Number(query.limit) || 10;
      const search = query.search;
      const role = query.role;
      const status = query.status;
      const sortBy = query.sortBy ?? 'createdAt';
      const sortOrder = query.sortOrder ?? 'desc';

      // Only admins can view all users
      if (requestingUserRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can view all users');
      }

      const skip = (page - 1) * limit;

      // Build where clause
      const whereClause: Record<string, unknown> = {};

      if (search) {
        whereClause.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { fullName: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (role) {
        // Normalize role value to match enum
        const normalizedRole = role.toUpperCase() as UserRole;
        if (Object.values(UserRole).includes(normalizedRole)) {
          whereClause.role = normalizedRole;
        }
      }

      if (status) {
        // Normalize status value to match enum
        const normalizedStatus = status.toUpperCase() as UserStatus;
        if (Object.values(UserStatus).includes(normalizedStatus)) {
          whereClause.status = normalizedStatus;
        }
      }

      const [users, total] = await Promise.all([
        this.prisma.user.findMany({
          where: whereClause,
          select: {
            id: true,
            email: true,
            fullName: true,
            role: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            lastLogin: true,
            _count: {
              select: {
                createdProjects: true,
                projectMemberships: true,
                generations: true,
              },
            },
          },
          orderBy: {
            [sortBy]: sortOrder,
          } as Record<string, 'asc' | 'desc'>,
          skip: Number(skip),
          take: Number(limit),
        }),
        this.prisma.user.count({ where: whereClause }),
      ]);

      return {
        message: 'Users retrieved successfully',
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          },
        },
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to retrieve users: ${(error as Error).message}`,
      );
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve users');
    }
  }

  async getUserById(
    userId: string,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Users can view their own profile, admins can view any profile
      if (requestingUserRole !== 'ADMIN' && requestingUserId !== userId) {
        throw new ForbiddenException('You can only view your own profile');
      }

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
          createdProjects: {
            select: {
              id: true,
              name: true,
              description: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          projectMemberships: {
            select: {
              project: {
                select: {
                  id: true,
                  name: true,
                  description: true,
                  createdAt: true,
                },
              },
            },
          },
          _count: {
            select: {
              createdProjects: true,
              projectMemberships: true,
              generations: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return {
        message: 'User retrieved successfully',
        data: user,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(`Failed to retrieve user: ${(error as Error).message}`);
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to retrieve user');
    }
  }

  async updateUserRole(
    userId: string,
    updateRoleDto: UpdateUserRoleDto,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Only admins can update user roles
      if (requestingUserRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can update user roles');
      }

      // Prevent admins from changing their own role
      if (requestingUserId === userId) {
        throw new ForbiddenException('You cannot change your own role');
      }

      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { role: updateRoleDto.role },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      this.logger.log(
        `User role updated: ${updatedUser.email} -> ${updateRoleDto.role}`,
      );

      return {
        message: 'User role updated successfully',
        data: updatedUser,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to update user role: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update user role');
    }
  }

  async toggleUserStatus(
    userId: string,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Only admins can toggle user status
      if (requestingUserRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can toggle user status');
      }

      // Prevent admins from deactivating themselves
      if (requestingUserId === userId) {
        throw new ForbiddenException('You cannot change your own status');
      }

      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // Toggle between ACTIVE and PENDING
      const newStatus: UserStatus =
        existingUser.status === UserStatus.ACTIVE
          ? UserStatus.PENDING
          : UserStatus.ACTIVE;

      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { status: newStatus },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      this.logger.log(
        `User status toggled: ${updatedUser.email} -> ${updatedUser.status}`,
      );

      return {
        message: `User ${
          updatedUser.status === UserStatus.ACTIVE ? 'activated' : 'deactivated'
        } successfully`,
        data: updatedUser,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to toggle user status: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to toggle user status');
    }
  }

  async updateUserStatus(
    userId: string,
    updateStatusDto: UpdateUserStatusDto,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Only admins can update user status
      if (requestingUserRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can update user status');
      }

      // Prevent admins from deactivating themselves
      if (
        requestingUserId === userId &&
        updateStatusDto.status !== UserStatus.ACTIVE
      ) {
        throw new ForbiddenException('You cannot deactivate your own account');
      }

      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { status: updateStatusDto.status },
        select: {
          id: true,
          email: true,
          fullName: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });

      this.logger.log(
        `User status updated: ${updatedUser.email} -> ${updatedUser.status}`,
      );

      return {
        message: `User status updated to ${updatedUser.status.toLowerCase()} successfully`,
        data: updatedUser,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to update user status: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update user status');
    }
  }

  async deleteUser(
    userId: string,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Only admins can delete users
      if (requestingUserRole !== 'ADMIN') {
        throw new ForbiddenException('Only admins can delete users');
      }

      // Prevent admins from deleting themselves
      if (requestingUserId === userId) {
        throw new ForbiddenException('You cannot delete your own account');
      }

      const existingUser = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!existingUser) {
        throw new NotFoundException('User not found');
      }

      // Soft delete by deactivating and marking email as deleted
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          status: UserStatus.PENDING,
          email: `deleted_${Date.now()}_${existingUser.email}`, // Prevent email conflicts
        },
      });

      this.logger.log(`User deleted: ${existingUser.email}`);

      return {
        message: 'User deleted successfully',
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(`Failed to delete user: ${(error as Error).message}`);
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to delete user');
    }
  }

  async getUserStats(
    userId: string,
    requestingUserId: string,
    requestingUserRole: string,
  ) {
    try {
      // Users can view their own stats, admins can view any user's stats
      if (requestingUserRole !== 'ADMIN' && requestingUserId !== userId) {
        throw new ForbiddenException('You can only view your own statistics');
      }

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      const stats = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          _count: {
            select: {
              createdProjects: true,
              projectMemberships: true,
              generations: true,
              promptHistory: true,
            },
          },
        },
      });

      if (!stats) {
        throw new NotFoundException('User statistics not found');
      }

      return {
        message: 'User statistics retrieved successfully',
        data: stats._count,
        statusCode: HttpStatus.OK,
      };
    } catch (error: unknown) {
      this.logger.error(
        `Failed to retrieve user stats: ${(error as Error).message}`,
      );
      if (
        error instanceof NotFoundException ||
        error instanceof ForbiddenException
      ) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to retrieve user statistics',
      );
    }
  }
}
