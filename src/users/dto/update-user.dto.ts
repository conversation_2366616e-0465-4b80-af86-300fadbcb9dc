import {
  IsOptional,
  IsEnum,
  IsString,
  IsE<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { UserRole, UserStatus } from 'generated/prisma';

export class UpdateUserDto {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsString()
  @MinLength(8)
  password?: string;

  @IsOptional()
  @IsString()
  fullName?: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @IsOptional()
  @IsEnum(UserStatus)
  status?: UserStatus;
}
