import {
  Body,
  Controller,
  Param,
  Put,
  Get,
  Post,
  Delete,
  Patch,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { UpdateUserDto } from 'src/users/dto/update-user.dto';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { UpdateUserRoleDto } from 'src/users/dto/update-user-role.dto';
import { UpdateUserStatusDto } from 'src/users/dto/update-user-status.dto';
import { GetUsersQueryDto } from 'src/users/dto/get-users-query.dto';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { AuthGuard } from 'src/auth/guards/auth.guard';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { AuthenticatedRequest } from 'src/auth/interfaces/authenticated-request.interface';

@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Post()
  @Roles(['ADMIN'])
  async createUser(@Body() createUserDto: CreateUserDto) {
    return this.usersService.createUser(createUserDto);
  }

  @Get()
  @Roles(['ADMIN'])
  async getAllUsers(
    @Query() query: GetUsersQueryDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.getAllUsers(query, req.user.sub, req.user.role);
  }

  @Get(':id')
  async getUserById(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.getUserById(id, req.user.sub, req.user.role);
  }

  @Get(':id/stats')
  async getUserStats(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.getUserStats(id, req.user.sub, req.user.role);
  }

  @Put(':id')
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.updateUser(
      id,
      updateUserDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Patch(':id/role')
  @Roles(['ADMIN'])
  async updateUserRole(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateUserRoleDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.updateUserRole(
      id,
      updateRoleDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Patch(':id/status')
  @Roles(['ADMIN'])
  async updateUserStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateUserStatusDto,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.updateUserStatus(
      id,
      updateStatusDto,
      req.user.sub,
      req.user.role,
    );
  }

  @Patch(':id/toggle-status')
  @Roles(['ADMIN'])
  async toggleUserStatus(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.toggleUserStatus(id, req.user.sub, req.user.role);
  }

  @Delete(':id')
  @Roles(['ADMIN'])
  async deleteUser(
    @Param('id') id: string,
    @Request() req: AuthenticatedRequest,
  ) {
    return this.usersService.deleteUser(id, req.user.sub, req.user.role);
  }
}
