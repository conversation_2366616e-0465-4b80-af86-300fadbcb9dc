import { JwtService } from '@nestjs/jwt';
import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Socket, Server } from 'socket.io';
import { GenerationService } from 'src/generation/generation.service';
import { JoinPayloadDto } from './dto/join-payload.dto';
import { ProjectService } from 'src/project/project.service';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { EditPayloadDto } from './dto/edit-payload.dto';
import { SavePayloadDto } from './dto/save-payload.dto';

@WebSocketGateway({ namespace: 'collaboration', cors: true })
export class CollaborationGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;
  private readonly logger = new Logger(CollaborationGateway.name);
  constructor(
    private readonly jwtService: JwtService,
    private readonly generationService: GenerationService,
    private readonly projectService: ProjectService,
  ) {}

  async handleConnection(client: Socket) {
    const token = client.handshake.auth?.token;
    if (!token) {
      client.disconnect(true);
      return;
    }
    try {
      const payload = await this.jwtService.verify(token);
      client.data.userId = payload.sub;
    } catch (error) {
      this.logger.error('Invalid token', error);
      client.disconnect(true);
    }
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('joinRoom')
  async handleJoin(
    @MessageBody() { generationId }: JoinPayloadDto,
    @ConnectedSocket() client: Socket,
  ) {
    const userId = client.data.userId;
    const hasAccess = await this.projectService.isMember(userId);
    if (!hasAccess) {
      throw new UnauthorizedException();
    }

    await client.join(generationId);
    this.server.to(generationId).emit('userJoined', { userId });
  }

  @SubscribeMessage('leaveRoom')
  async handleLeave(
    @MessageBody() { generationId }: JoinPayloadDto,
    @ConnectedSocket() client: Socket,
  ) {
    await client.leave(generationId);
    this.server
      .to(generationId)
      .emit('userLeft', { userId: client.data.userId });
  }

  @SubscribeMessage('edit')
  handleEdit(
    @MessageBody() { generationId, changes }: EditPayloadDto,
    @ConnectedSocket() client: Socket,
  ) {
    client
      .to(generationId)
      .emit('remoteEdit', { userId: client.data.userId, changes });
  }

  @SubscribeMessage('save')
  async handleSave(
    @MessageBody() savePayloadDto: SavePayloadDto,
    @ConnectedSocket() client: Socket,
  ) {
    const userId = client.data.userId;

    const canSave = await this.projectService.isMember(userId);
    if (!canSave) {
      throw new UnauthorizedException();
    }
    await this.generationService.saveDocument(savePayloadDto);

    this.server.to(savePayloadDto.generationId).emit('documentSaved', {
      content: savePayloadDto.content,
      savedBy: userId,
    });
  }
}
