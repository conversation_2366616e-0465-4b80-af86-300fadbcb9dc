import { Modu<PERSON> } from '@nestjs/common';
import { CollaborationGateway } from './collaboration.gateway';
import { GenerationModule } from 'src/generation/generation.module';
import { ProjectService } from 'src/project/project.service';
import { PrismaService } from 'src/prisma.service';
import { AssistantModule } from 'src/assistant/assistant.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    GenerationModule,
    AssistantModule,
    ConfigModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default_secret',
      signOptions: { expiresIn: '1h' },
    }),
  ],
  providers: [CollaborationGateway, ProjectService, PrismaService],
})
export class CollaborationModule {}
