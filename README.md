# PixiGenerator (Nest.js backend)
## Installation des dépendances

```bash
$ npm install
```

## Compilation et exécution du project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Exécution des tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Développement

On a utilisé Docker pour lancer une instance PostgreSQL avec l'utilisateur `postgres`, mot de passe `postgres` et en se basant sur l'image `postgres:alpine` comme suit:

Il faut spécifier `DATABASE_URL` dans le fichier `.env` comme indiquant le fichier `.env.example`

```bash
docker run --rm -P -p 127.0.0.1:5432:5432 --name postgres -e POSTGRES_PASSWORD=postgres -d postgres:alpine
```

On a utilisé Docker pour lancer une instance smtp4dev qui permet d'utiliser et tester un serveur smtp:

Le fichier `.env.example` contient les variables d'environnement nécessaires pour se connecter à l'instance une fois lancée.

Pour lancer l'instance:

```bash
docker run -p 5000:80 -p 2525:25 -d rnwood/smtp4dev
```

Pour accéder à l'inbox: `localhost:5000`