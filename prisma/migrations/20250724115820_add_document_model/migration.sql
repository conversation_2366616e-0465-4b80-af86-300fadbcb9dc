-- CreateTable
CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "generationId" TEXT NOT NULL,
    "content" JSONB NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Document_generationId_key" ON "Document"("generationId");

-- AddF<PERSON>ignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_generationId_fkey" FOREIGN KEY ("generationId") REFERENCES "generations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
