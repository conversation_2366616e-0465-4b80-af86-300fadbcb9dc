generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                 String                @id @default(cuid())
  email              String                @unique
  fullName           String?
  passwordHash       String
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  lastLogin          DateTime?
  status             UserStatus            @default(PENDING)
  role               UserRole              @default(USER)
  invitationId       String?               @unique
  messages           ConversationMessage[] @relation("MessageCreator")
  generations        Generation[]          @relation("GenerationCreator")
  invitations        Invitation[]          @relation("InviterInvitations")
  projectMemberships ProjectMember[]
  createdProjects    Project[]             @relation("ProjectCreator")
  promptHistory      PromptHistory[]       @relation("PromptCreator")
  refreshToken       RefreshToken?
  invitation         Invitation?           @relation("UserInvitations", fields: [invitationId], references: [id])

  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  value     String   @unique
  user      User     @relation(fields: [userId], references: [id])

  @@map("refresh_tokens")
}

model Invitation {
  id          String           @id @default(cuid())
  email       String           @unique
  token       String           @unique
  status      InvitationStatus @default(PENDING)
  userId      String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  invitedById String?
  invitedBy   User?            @relation("InviterInvitations", fields: [userId], references: [id])
  invitee     User?            @relation("UserInvitations")

  @@map("invitations")
}

model Project {
  id            String          @id @default(cuid())
  name          String
  description   String?
  status        ProjectStatus   @default(ACTIVE)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  createdById   String
  generations   Generation[]
  members       ProjectMember[]
  createdBy     User            @relation("ProjectCreator", fields: [createdById], references: [id])
  promptHistory PromptHistory[]

  @@map("projects")
}

model ProjectMember {
  id        String   @id @default(cuid())
  projectId String
  userId    String
  joinedAt  DateTime @default(now())
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model Generation {
  id              String                @id @default(cuid())
  type            GenerationType
  prompt          String
  status          GenerationStatus      @default(PENDING)
  result          String?
  metadata        Json?
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt
  projectId       String
  createdById     String
  promptHistoryId String?
  currentResult   String?
  initialPrompt   String?
  name            String?
  conversation    ConversationMessage[] @relation("GenerationConversation")
  createdBy       User                  @relation("GenerationCreator", fields: [createdById], references: [id])
  project         Project               @relation(fields: [projectId], references: [id], onDelete: Cascade)
  promptHistory   PromptHistory?        @relation(fields: [promptHistoryId], references: [id])
  Document        Document?

  @@map("generations")
}

model PromptHistory {
  id          String         @id @default(cuid())
  prompt      String
  type        GenerationType
  description String?
  tags        String[]
  usageCount  Int            @default(0)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  projectId   String?
  createdById String
  generations Generation[]
  createdBy   User           @relation("PromptCreator", fields: [createdById], references: [id])
  project     Project?       @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("prompt_history")
}

model ConversationMessage {
  id             String        @id @default(cuid())
  role           MessageRole
  content        String
  status         MessageStatus @default(SENT)
  inputData      Json?
  outputData     Json?
  errorMessage   String?
  messageIndex   Int
  createdAt      DateTime      @default(now())
  processingTime Int?
  generationId   String
  createdById    String?
  createdBy      User?         @relation("MessageCreator", fields: [createdById], references: [id])
  generation     Generation    @relation("GenerationConversation", fields: [generationId], references: [id], onDelete: Cascade)

  @@index([generationId, messageIndex])
  @@map("conversation_messages")
}

model Document {
  id           String   @id @default(cuid())
  generationId String   @unique
  content      Json
  updatedAt    DateTime @updatedAt

  generation Generation @relation(fields: [generationId], references: [id], onDelete: Cascade)
}

enum UserRole {
  USER
  ADMIN
}

enum UserStatus {
  ACTIVE
  PENDING
  REJECTED
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum ProjectStatus {
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum GenerationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum GenerationType {
  UI
  DOCUMENTATION
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum MessageStatus {
  SENT
  PROCESSING
  COMPLETED
  FAILED
}
